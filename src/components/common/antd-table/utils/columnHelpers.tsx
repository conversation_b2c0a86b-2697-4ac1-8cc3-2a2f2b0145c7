import React from 'react';
import { Tag, Image, Typography, Tooltip, Space } from 'antd';
import { BaseTableColumn } from '../types';
import dayjs from 'dayjs';

const { Text } = Typography;

// Helper function to create a text column
export const createTextColumn = <T,>(
  key: string,
  title: string,
  dataIndex?: string,
  options?: {
    width?: number | string;
    ellipsis?: boolean;
    copyable?: boolean;
    maxLength?: number;
    tooltip?: boolean;
  }
): BaseTableColumn<T> => ({
  key,
  title,
  dataIndex: dataIndex || key,
  width: options?.width,
  ellipsis: options?.ellipsis,
  render: (text: string) => {
    if (!text) return '-';
    
    const displayText = options?.maxLength && text.length > options.maxLength
      ? `${text.slice(0, options.maxLength)}...`
      : text;
    
    const textElement = (
      <Text copyable={options?.copyable}>
        {displayText}
      </Text>
    );
    
    if (options?.tooltip && options?.maxLength && text.length > options.maxLength) {
      return <Tooltip title={text}>{textElement}</Tooltip>;
    }
    
    return textElement;
  },
});

// Helper function to create a number column
export const createNumberColumn = <T,>(
  key: string,
  title: string,
  dataIndex?: string,
  options?: {
    width?: number | string;
    format?: 'currency' | 'percentage' | 'decimal';
    precision?: number;
    suffix?: string;
    prefix?: string;
  }
): BaseTableColumn<T> => ({
  key,
  title,
  dataIndex: dataIndex || key,
  width: options?.width,
  align: 'right',
  render: (value: number) => {
    if (value === null || value === undefined) return '-';
    
    let formattedValue = value;
    
    if (options?.format === 'currency') {
      return new Intl.NumberFormat('vi-VN', {
        style: 'currency',
        currency: 'VND',
      }).format(value);
    }
    
    if (options?.format === 'percentage') {
      return `${(value * 100).toFixed(options?.precision || 2)}%`;
    }
    
    if (options?.precision !== undefined) {
      formattedValue = Number(value.toFixed(options.precision));
    }
    
    const formatted = new Intl.NumberFormat('vi-VN').format(formattedValue);
    
    return `${options?.prefix || ''}${formatted}${options?.suffix || ''}`;
  },
});

// Helper function to create a date column
export const createDateColumn = <T,>(
  key: string,
  title: string,
  dataIndex?: string,
  options?: {
    width?: number | string;
    format?: string;
    relative?: boolean;
  }
): BaseTableColumn<T> => ({
  key,
  title,
  dataIndex: dataIndex || key,
  width: options?.width,
  render: (date: string | Date) => {
    if (!date) return '-';
    
    const dayjsDate = dayjs(date);
    
    if (!dayjsDate.isValid()) return '-';
    
    if (options?.relative) {
      return (
        <Tooltip title={dayjsDate.format(options?.format || 'DD/MM/YYYY HH:mm')}>
          {dayjsDate.fromNow()}
        </Tooltip>
      );
    }
    
    return dayjsDate.format(options?.format || 'DD/MM/YYYY');
  },
});

// Helper function to create a status column with tags
export const createStatusColumn = <T,>(
  key: string,
  title: string,
  dataIndex?: string,
  options?: {
    width?: number | string;
    statusMap?: Record<string, { color: string; text: string }>;
    defaultColor?: string;
  }
): BaseTableColumn<T> => ({
  key,
  title,
  dataIndex: dataIndex || key,
  width: options?.width,
  align: 'center',
  render: (status: string) => {
    if (!status) return '-';
    
    const statusConfig = options?.statusMap?.[status];
    const color = statusConfig?.color || options?.defaultColor || 'default';
    const text = statusConfig?.text || status;
    
    return <Tag color={color}>{text}</Tag>;
  },
});

// Helper function to create an image column
export const createImageColumn = <T,>(
  key: string,
  title: string,
  dataIndex?: string,
  options?: {
    width?: number | string;
    imageWidth?: number;
    imageHeight?: number;
    fallback?: string;
    preview?: boolean;
  }
): BaseTableColumn<T> => ({
  key,
  title,
  dataIndex: dataIndex || key,
  width: options?.width,
  align: 'center',
  render: (src: string) => {
    if (!src) return '-';
    
    return (
      <Image
        src={src}
        alt={title}
        width={options?.imageWidth || 40}
        height={options?.imageHeight || 40}
        style={{ objectFit: 'cover', borderRadius: 4 }}
        fallback={options?.fallback}
        preview={options?.preview !== false}
      />
    );
  },
});

// Helper function to create a boolean column
export const createBooleanColumn = <T,>(
  key: string,
  title: string,
  dataIndex?: string,
  options?: {
    width?: number | string;
    trueText?: string;
    falseText?: string;
    trueColor?: string;
    falseColor?: string;
  }
): BaseTableColumn<T> => ({
  key,
  title,
  dataIndex: dataIndex || key,
  width: options?.width,
  align: 'center',
  render: (value: boolean) => {
    const isTrue = Boolean(value);
    const text = isTrue ? (options?.trueText || 'Có') : (options?.falseText || 'Không');
    const color = isTrue ? (options?.trueColor || 'success') : (options?.falseColor || 'default');
    
    return <Tag color={color}>{text}</Tag>;
  },
});

// Helper function to create an index column
export const createIndexColumn = <T,>(
  title: string = 'STT',
  options?: {
    width?: number | string;
    startIndex?: number;
  }
): BaseTableColumn<T> => ({
  key: 'index',
  title,
  width: options?.width || 60,
  align: 'center',
  render: (_: any, __: T, index: number) => (options?.startIndex || 0) + index + 1,
});

// Helper function to create a link column
export const createLinkColumn = <T,>(
  key: string,
  title: string,
  dataIndex?: string,
  options?: {
    width?: number | string;
    href?: (record: T) => string;
    onClick?: (record: T) => void;
    target?: string;
    maxLength?: number;
  }
): BaseTableColumn<T> => ({
  key,
  title,
  dataIndex: dataIndex || key,
  width: options?.width,
  render: (text: string, record: T) => {
    if (!text) return '-';
    
    const displayText = options?.maxLength && text.length > options.maxLength
      ? `${text.slice(0, options.maxLength)}...`
      : text;
    
    if (options?.onClick) {
      return (
        <Typography.Link onClick={() => options.onClick!(record)}>
          {displayText}
        </Typography.Link>
      );
    }
    
    if (options?.href) {
      return (
        <Typography.Link href={options.href(record)} target={options?.target}>
          {displayText}
        </Typography.Link>
      );
    }
    
    return text;
  },
});
