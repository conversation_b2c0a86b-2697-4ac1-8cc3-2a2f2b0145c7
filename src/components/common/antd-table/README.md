# CommonTable - Ant Design Table Component

Một component Table chung được xây dựng trên Ant Design, cung cấp các tính năng phổ biến và có thể tái sử dụng cho toàn bộ hệ thống.

## Tính năng chính

- ✅ Pagination tự động
- ✅ Row selection (checkbox/radio)
- ✅ Actions column với dropdown
- ✅ Search/Filter
- ✅ Toolbar với các action buttons
- ✅ Empty state tùy chỉnh
- ✅ Loading state
- ✅ Responsive design
- ✅ Column helpers (text, number, date, status, image, boolean)
- ✅ TypeScript support

## Cài đặt

Component đã được cài đặt sẵn trong dự án. <PERSON><PERSON> sử dụng, import từ:

```typescript
import {
  CommonTable,
  useTablePagination,
  useTableSelection,
  useTableActions,
  createTextColumn,
  createNumberColumn,
  // ... other utilities
} from '@/src/components/common/antd-table';
```

## Sử dụng cơ bản

```typescript
import React from 'react';
import { CommonTable, createTextColumn, createNumberColumn } from '@/src/components/common/antd-table';

const MyComponent = () => {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);

  const columns = [
    createTextColumn('name', 'Tên'),
    createNumberColumn('price', 'Giá', 'price', { format: 'currency' }),
    createTextColumn('description', 'Mô tả', 'description', { ellipsis: true }),
  ];

  return (
    <CommonTable
      columns={columns}
      data={data}
      loading={loading}
      pagination={{
        current: 1,
        pageSize: 10,
        total: 100,
        onChange: (page, pageSize) => {
          // Handle pagination
        },
      }}
    />
  );
};
```

## Sử dụng với Hooks

### useTablePagination

```typescript
const { pagination, setTotal } = useTablePagination({
  initialPage: 1,
  initialPageSize: 10,
  onChange: (page, pageSize) => {
    fetchData(page, pageSize);
  },
});

// Cập nhật total khi có dữ liệu mới
useEffect(() => {
  setTotal(response.total);
}, [response]);
```

### useTableSelection

```typescript
const { rowSelection, selectedRows, clearSelection } = useTableSelection({
  rowKey: 'id',
  onSelectionChange: (keys, rows) => {
    console.log('Selected:', keys, rows);
  },
  getCheckboxProps: (record) => ({
    disabled: record.status === 'disabled',
  }),
});
```

### useTableActions

```typescript
const actions = useTableActions({
  onEdit: (record) => handleEdit(record),
  onDelete: (record) => handleDelete(record),
  canEdit: (record) => hasPermission('edit'),
  canDelete: (record) => hasPermission('delete') && record.canDelete,
  customActions: [
    {
      key: 'view',
      label: 'Xem',
      icon: <EyeOutlined />,
      onClick: (record) => handleView(record),
    },
  ],
});
```

## Column Helpers

### createTextColumn
```typescript
createTextColumn('name', 'Tên', 'name', {
  width: 200,
  ellipsis: true,
  maxLength: 50,
  tooltip: true,
  copyable: true,
})
```

### createNumberColumn
```typescript
createNumberColumn('price', 'Giá', 'price', {
  format: 'currency', // 'currency' | 'percentage' | 'decimal'
  precision: 2,
  prefix: '$',
  suffix: ' VND',
})
```

### createDateColumn
```typescript
createDateColumn('createdAt', 'Ngày tạo', 'createdAt', {
  format: 'DD/MM/YYYY HH:mm',
  relative: true, // Hiển thị thời gian tương đối (2 hours ago)
})
```

### createStatusColumn
```typescript
createStatusColumn('status', 'Trạng thái', 'status', {
  statusMap: {
    'active': { color: 'success', text: 'Hoạt động' },
    'inactive': { color: 'default', text: 'Không hoạt động' },
  },
})
```

### createImageColumn
```typescript
createImageColumn('avatar', 'Ảnh đại diện', 'avatar', {
  imageWidth: 40,
  imageHeight: 40,
  preview: true,
  fallback: '/default-avatar.png',
})
```

## Ví dụ đầy đủ

```typescript
import React, { useState, useEffect, useRef } from 'react';
import { Button, Space, Modal, message } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import {
  CommonTable,
  useTablePagination,
  useTableSelection,
  useTableActions,
  createIndexColumn,
  createTextColumn,
  createNumberColumn,
  createDateColumn,
  createStatusColumn,
  TableRef,
} from '@/src/components/common/antd-table';

interface User {
  id: string;
  name: string;
  email: string;
  status: 'active' | 'inactive';
  createdAt: string;
  orderCount: number;
}

const UserManagement = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const tableRef = useRef<TableRef>(null);

  // Pagination
  const { pagination, setTotal } = useTablePagination({
    onChange: (page, pageSize) => fetchUsers(page, pageSize),
  });

  // Selection
  const { rowSelection, selectedRows, clearSelection } = useTableSelection<User>({
    rowKey: 'id',
  });

  // Actions
  const actions = useTableActions<User>({
    onEdit: handleEdit,
    onDelete: handleDelete,
    canEdit: () => true,
    canDelete: (record) => record.status !== 'active',
  });

  // Columns
  const columns = [
    createIndexColumn('STT'),
    createTextColumn<User>('name', 'Tên', 'name', { ellipsis: true }),
    createTextColumn<User>('email', 'Email', 'email'),
    createStatusColumn<User>('status', 'Trạng thái', 'status', {
      statusMap: {
        'active': { color: 'success', text: 'Hoạt động' },
        'inactive': { color: 'default', text: 'Không hoạt động' },
      },
    }),
    createNumberColumn<User>('orderCount', 'Số đơn hàng', 'orderCount'),
    createDateColumn<User>('createdAt', 'Ngày tạo', 'createdAt'),
  ];

  const fetchUsers = async (page = 1, pageSize = 10) => {
    setLoading(true);
    try {
      const response = await api.getUsers({
        page: page - 1,
        size: pageSize,
        search: searchTerm,
      });
      setUsers(response.data);
      setTotal(response.total);
    } catch (error) {
      message.error('Lỗi khi tải dữ liệu');
    } finally {
      setLoading(false);
    }
  };

  function handleEdit(user: User) {
    // Handle edit
  }

  function handleDelete(user: User) {
    Modal.confirm({
      title: 'Xác nhận xóa',
      content: `Bạn có chắc chắn muốn xóa người dùng "${user.name}" không?`,
      onOk: async () => {
        try {
          await api.deleteUser(user.id);
          message.success('Xóa thành công');
          fetchUsers();
        } catch (error) {
          message.error('Xóa thất bại');
        }
      },
    });
  }

  const handleDeleteMultiple = () => {
    Modal.confirm({
      title: 'Xác nhận xóa',
      content: `Bạn có chắc chắn muốn xóa ${selectedRows.length} người dùng đã chọn không?`,
      onOk: async () => {
        try {
          await api.deleteUsers(selectedRows.map(u => u.id));
          message.success('Xóa thành công');
          clearSelection();
          fetchUsers();
        } catch (error) {
          message.error('Xóa thất bại');
        }
      },
    });
  };

  useEffect(() => {
    fetchUsers();
  }, [searchTerm]);

  return (
    <CommonTable
      ref={tableRef}
      columns={columns}
      data={users}
      loading={loading}
      pagination={pagination}
      rowSelection={rowSelection}
      actions={actions}
      searchable
      searchPlaceholder="Tìm kiếm người dùng..."
      onSearch={setSearchTerm}
      toolbar={{
        title: 'Quản lý người dùng',
        extra: (
          <Space>
            {selectedRows.length > 0 && (
              <Button danger onClick={handleDeleteMultiple}>
                Xóa đã chọn ({selectedRows.length})
              </Button>
            )}
            <Button type="primary" icon={<PlusOutlined />}>
              Thêm người dùng
            </Button>
          </Space>
        ),
        showRefresh: true,
        onRefresh: fetchUsers,
      }}
      emptyText="Không có người dùng nào"
      emptyAction={{
        text: 'Thêm người dùng đầu tiên',
        onClick: () => console.log('Add first user'),
      }}
    />
  );
};
```

## Props API

### CommonTableProps

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| columns | BaseTableColumn[] | - | Cấu hình các cột |
| data | T[] | - | Dữ liệu hiển thị |
| loading | boolean | false | Trạng thái loading |
| pagination | object \| false | - | Cấu hình pagination |
| rowSelection | object | - | Cấu hình row selection |
| actions | TableAction[] | - | Các action cho mỗi row |
| searchable | boolean | false | Hiển thị ô tìm kiếm |
| toolbar | object | - | Cấu hình toolbar |
| emptyText | string | 'Không có dữ liệu' | Text hiển thị khi không có dữ liệu |

Xem file `types.ts` để biết chi tiết đầy đủ về các props.

## Migration từ Material-UI Table

Để migrate từ Material-UI Table sang CommonTable:

1. Thay thế import Material-UI bằng CommonTable
2. Chuyển đổi columns từ TableCell sang BaseTableColumn
3. Sử dụng các column helpers thay vì render thủ công
4. Thay thế TablePagination bằng pagination prop
5. Sử dụng rowSelection thay vì Checkbox thủ công

## Best Practices

1. Sử dụng column helpers thay vì viết render function thủ công
2. Sử dụng các hooks để quản lý state
3. Implement proper error handling
4. Sử dụng TypeScript để type safety
5. Optimize performance với useMemo cho columns
6. Sử dụng ref để access table methods khi cần
