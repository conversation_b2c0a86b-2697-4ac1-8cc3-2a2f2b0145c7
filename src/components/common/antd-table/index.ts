// Main component
export { default as CommonTable } from './CommonTable';

// Types
export type {
  BaseTableColumn,
  TableAction,
  CommonTableProps,
  TableRef,
} from './types';

// Hooks
export { useTableActions } from './hooks/useTableActions';
export { useTablePagination } from './hooks/useTablePagination';
export { useTableSelection } from './hooks/useTableSelection';

// Utilities
export {
  createTextColumn,
  createNumberColumn,
  createDateColumn,
  createStatusColumn,
  createImageColumn,
  createBooleanColumn,
  createIndexColumn,
  createLinkColumn,
} from './utils/columnHelpers';
