import React, { useMemo } from "react";
import { ViewIcon, EditIcon, DeleteIcon } from "../utils/iconHelpers";
import { TableAction } from "../types";

interface UseTableActionsProps<T> {
  onEdit?: (record: T) => void;
  onDelete?: (record: T) => void;
  onView?: (record: T) => void;
  canEdit?: (record: T) => boolean;
  canDelete?: (record: T) => boolean;
  canView?: (record: T) => boolean;
  customActions?: TableAction<T>[];
}

export const useTableActions = <T = any>({
  onEdit,
  onDelete,
  onView,
  canEdit,
  canDelete,
  canView,
  customActions = [],
}: UseTableActionsProps<T> = {}): TableAction<T>[] => {
  return useMemo(() => {
    const actions: TableAction<T>[] = [];

    // View action
    if (onView) {
      actions.push({
        key: "view",
        label: "Xem",
        icon: <ViewIcon />,
        onClick: onView,
        visible: canView,
        tooltip: "Xem chi tiết",
      });
    }

    // Edit action
    if (onEdit) {
      actions.push({
        key: "edit",
        label: "Sửa",
        icon: <EditIcon />,
        onClick: onEdit,
        visible: canEdit,
        tooltip: "Chỉnh sửa",
        type: "primary",
      });
    }

    // Delete action
    if (onDelete) {
      actions.push({
        key: "delete",
        label: "Xóa",
        icon: <DeleteIcon />,
        onClick: onDelete,
        visible: canDelete,
        tooltip: "Xóa",
        type: "danger",
      });
    }

    // Add custom actions
    actions.push(...customActions);

    return actions;
  }, [onEdit, onDelete, onView, canEdit, canDelete, canView, customActions]);
};
