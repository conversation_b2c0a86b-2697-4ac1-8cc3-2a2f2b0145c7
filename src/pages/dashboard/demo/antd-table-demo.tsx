import React, { useState, useEffect, useRef } from 'react';
import { Button, Space, Modal, message, Card } from 'antd';
import { PlusOutlined, UserOutlined } from '@ant-design/icons';
import DashboardLayout from '@/src/layouts/dashboard';
import {
  CommonTable,
  useTablePagination,
  useTableSelection,
  useTableActions,
  createIndexColumn,
  createTextColumn,
  createNumberColumn,
  createDateColumn,
  createStatusColumn,
  createBooleanColumn,
  createImageColumn,
  TableRef,
} from '@/src/components/common/antd-table';

interface DemoUser {
  id: string;
  name: string;
  email: string;
  status: 'active' | 'inactive' | 'pending';
  isVip: boolean;
  orderCount: number;
  totalSpent: number;
  avatar: string;
  createdAt: string;
  lastLogin: string;
}

// Mock data
const generateMockData = (count: number): DemoUser[] => {
  const statuses: ('active' | 'inactive' | 'pending')[] = ['active', 'inactive', 'pending'];
  const names = ['<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>n E'];
  
  return Array.from({ length: count }, (_, index) => ({
    id: `user-${index + 1}`,
    name: names[index % names.length] + ` ${index + 1}`,
    email: `user${index + 1}@example.com`,
    status: statuses[index % statuses.length],
    isVip: Math.random() > 0.7,
    orderCount: Math.floor(Math.random() * 50),
    totalSpent: Math.floor(Math.random() * 10000000),
    avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=${index}`,
    createdAt: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString(),
    lastLogin: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
  }));
};

const AntdTableDemo = () => {
  const [users, setUsers] = useState<DemoUser[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const tableRef = useRef<TableRef>(null);

  // Pagination
  const { pagination, setTotal } = useTablePagination({
    initialPageSize: 10,
    onChange: (page, pageSize) => {
      console.log('Pagination changed:', page, pageSize);
      fetchUsers(page, pageSize);
    },
  });

  // Selection
  const { rowSelection, selectedRows, clearSelection } = useTableSelection<DemoUser>({
    rowKey: 'id',
    onSelectionChange: (keys, rows) => {
      console.log('Selection changed:', keys, rows);
    },
    getCheckboxProps: (record) => ({
      disabled: record.status === 'inactive',
    }),
  });

  // Actions
  const actions = useTableActions<DemoUser>({
    onEdit: handleEdit,
    onDelete: handleDelete,
    onView: handleView,
    canEdit: () => true,
    canDelete: (record) => record.status !== 'active',
    canView: () => true,
    customActions: [
      {
        key: 'promote',
        label: 'Thăng hạng VIP',
        icon: <UserOutlined />,
        onClick: handlePromoteVip,
        visible: (record) => !record.isVip,
        type: 'primary',
      },
    ],
  });

  // Columns
  const columns = [
    createIndexColumn('STT', { width: 60 }),
    createImageColumn<DemoUser>('avatar', 'Avatar', 'avatar', {
      width: 80,
      imageWidth: 40,
      imageHeight: 40,
      preview: true,
    }),
    createTextColumn<DemoUser>('name', 'Tên', 'name', {
      width: 150,
      ellipsis: true,
      maxLength: 20,
      tooltip: true,
    }),
    createTextColumn<DemoUser>('email', 'Email', 'email', {
      width: 200,
      ellipsis: true,
      copyable: true,
    }),
    createStatusColumn<DemoUser>('status', 'Trạng thái', 'status', {
      width: 120,
      statusMap: {
        'active': { color: 'success', text: 'Hoạt động' },
        'inactive': { color: 'default', text: 'Không hoạt động' },
        'pending': { color: 'warning', text: 'Chờ duyệt' },
      },
    }),
    createBooleanColumn<DemoUser>('isVip', 'VIP', 'isVip', {
      width: 80,
      trueText: 'VIP',
      falseText: 'Thường',
      trueColor: 'gold',
      falseColor: 'default',
    }),
    createNumberColumn<DemoUser>('orderCount', 'Số đơn hàng', 'orderCount', {
      width: 120,
    }),
    createNumberColumn<DemoUser>('totalSpent', 'Tổng chi tiêu', 'totalSpent', {
      width: 150,
      format: 'currency',
    }),
    createDateColumn<DemoUser>('createdAt', 'Ngày tạo', 'createdAt', {
      width: 120,
      format: 'DD/MM/YYYY',
    }),
    createDateColumn<DemoUser>('lastLogin', 'Lần cuối đăng nhập', 'lastLogin', {
      width: 150,
      relative: true,
    }),
  ];

  // Mock API calls
  const fetchUsers = async (page = 1, pageSize = 10) => {
    setLoading(true);
    
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    try {
      const allData = generateMockData(100);
      
      // Filter by search term
      const filteredData = searchTerm
        ? allData.filter(user => 
            user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            user.email.toLowerCase().includes(searchTerm.toLowerCase())
          )
        : allData;
      
      // Paginate
      const startIndex = (page - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      const paginatedData = filteredData.slice(startIndex, endIndex);
      
      setUsers(paginatedData);
      setTotal(filteredData.length);
    } catch (error) {
      message.error('Lỗi khi tải dữ liệu');
    } finally {
      setLoading(false);
    }
  };

  function handleEdit(user: DemoUser) {
    message.info(`Chỉnh sửa người dùng: ${user.name}`);
  }

  function handleDelete(user: DemoUser) {
    Modal.confirm({
      title: 'Xác nhận xóa',
      content: `Bạn có chắc chắn muốn xóa người dùng "${user.name}" không?`,
      okText: 'Xóa',
      cancelText: 'Hủy',
      okType: 'danger',
      onOk: async () => {
        try {
          // Simulate API call
          await new Promise(resolve => setTimeout(resolve, 500));
          message.success('Xóa thành công');
          fetchUsers();
        } catch (error) {
          message.error('Xóa thất bại');
        }
      },
    });
  }

  function handleView(user: DemoUser) {
    Modal.info({
      title: 'Thông tin người dùng',
      content: (
        <div>
          <p><strong>Tên:</strong> {user.name}</p>
          <p><strong>Email:</strong> {user.email}</p>
          <p><strong>Trạng thái:</strong> {user.status}</p>
          <p><strong>VIP:</strong> {user.isVip ? 'Có' : 'Không'}</p>
          <p><strong>Số đơn hàng:</strong> {user.orderCount}</p>
          <p><strong>Tổng chi tiêu:</strong> {new Intl.NumberFormat('vi-VN', {
            style: 'currency',
            currency: 'VND',
          }).format(user.totalSpent)}</p>
        </div>
      ),
      width: 500,
    });
  }

  function handlePromoteVip(user: DemoUser) {
    Modal.confirm({
      title: 'Thăng hạng VIP',
      content: `Bạn có muốn thăng hạng VIP cho người dùng "${user.name}" không?`,
      okText: 'Thăng hạng',
      cancelText: 'Hủy',
      onOk: async () => {
        try {
          // Simulate API call
          await new Promise(resolve => setTimeout(resolve, 500));
          message.success('Thăng hạng VIP thành công');
          fetchUsers();
        } catch (error) {
          message.error('Thăng hạng thất bại');
        }
      },
    });
  }

  const handleDeleteMultiple = () => {
    if (selectedRows.length === 0) return;
    
    Modal.confirm({
      title: 'Xác nhận xóa nhiều người dùng',
      content: `Bạn có chắc chắn muốn xóa ${selectedRows.length} người dùng đã chọn không?`,
      okText: 'Xóa',
      cancelText: 'Hủy',
      okType: 'danger',
      onOk: async () => {
        try {
          // Simulate API call
          await new Promise(resolve => setTimeout(resolve, 1000));
          message.success(`Xóa ${selectedRows.length} người dùng thành công`);
          clearSelection();
          fetchUsers();
        } catch (error) {
          message.error('Xóa thất bại');
        }
      },
    });
  };

  const handleAddUser = () => {
    message.info('Chức năng thêm người dùng');
  };

  useEffect(() => {
    fetchUsers();
  }, [searchTerm]);

  return (
    <DashboardLayout>
      <div style={{ padding: 24 }}>
        <Card title="Demo Ant Design Table Component" style={{ marginBottom: 16 }}>
          <p>
            Đây là trang demo cho CommonTable component được xây dựng trên Ant Design.
            Component này cung cấp các tính năng:
          </p>
          <ul>
            <li>Pagination tự động</li>
            <li>Row selection với checkbox</li>
            <li>Actions column với các nút thao tác</li>
            <li>Search/Filter</li>
            <li>Toolbar với các action buttons</li>
            <li>Empty state</li>
            <li>Loading state</li>
            <li>Column helpers (text, number, date, status, image, boolean)</li>
          </ul>
        </Card>

        <CommonTable
          ref={tableRef}
          columns={columns}
          data={users}
          loading={loading}
          pagination={pagination}
          rowSelection={rowSelection}
          actions={actions}
          searchable
          searchPlaceholder="Tìm kiếm theo tên hoặc email..."
          onSearch={setSearchTerm}
          toolbar={{
            title: 'Quản lý người dùng',
            extra: (
              <Space>
                {selectedRows.length > 0 && (
                  <Button danger onClick={handleDeleteMultiple}>
                    Xóa đã chọn ({selectedRows.length})
                  </Button>
                )}
                <Button type="primary" icon={<PlusOutlined />} onClick={handleAddUser}>
                  Thêm người dùng
                </Button>
              </Space>
            ),
            showRefresh: true,
            onRefresh: () => fetchUsers(),
          }}
          emptyText="Không có người dùng nào"
          emptyDescription="Hệ thống chưa có người dùng nào được tạo"
          emptyAction={{
            text: 'Thêm người dùng đầu tiên',
            onClick: handleAddUser,
          }}
          rowKey="id"
          size="middle"
          bordered
          scroll={{ x: 1200 }}
        />
      </div>
    </DashboardLayout>
  );
};

export default AntdTableDemo;
